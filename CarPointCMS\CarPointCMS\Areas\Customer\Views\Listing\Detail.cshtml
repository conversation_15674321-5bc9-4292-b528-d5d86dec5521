@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model ListingDetailViewModel
@{
    ViewData["Title"] = Model.Listing.ListingName;
    ViewData["Description"] = Model.Listing.ListingDescription?.Substring(0, Math.Min(160, Model.Listing.ListingDescription.Length));
}

<script type='text/javascript' src='https://platform-api.sharethis.com/js/sharethis.js#property=5993ef01e2587a001253a261&product=inline-share-buttons' async='async'></script>

<div class="listing-single-banner" style="background-image: url('~/uploads/listing_featured_photos/@Model.Listing.ListingFeaturedPhoto');">
    <div class="bg"></div>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>@Model.Listing.ListingName</h1>
                <div class="price">
                    @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                    {
                        @($"${Model.Listing.ListingPrice:N0}")
                    }
                    else
                    {
                        @($"{Model.CurrentCurrencySymbol}{(Model.Listing.ListingPrice * Model.CurrentCurrencyValue):N0}")
                    }
                </div>
                <div class="location">
                    <i class="fas fa-map-marker-alt"></i> @Model.Listing.ListingLocation.ListingLocationName
                </div>
                <div class="review">
                    @* Rating stars logic - same as home page *@
                    @{
                        var reviewCount = Model.Reviews.Count;
                        var overallRating = 0.0;

                        if(reviewCount > 0)
                        {
                            var totalRating = Model.Reviews.Sum(r => r.Rating);
                            overallRating = (double)totalRating / reviewCount;

                            // Round to nearest 0.5
                            if(overallRating > 0 && overallRating <= 1)
                                overallRating = 1;
                            else if(overallRating > 1 && overallRating <= 1.5)
                                overallRating = 1.5;
                            else if(overallRating > 1.5 && overallRating <= 2)
                                overallRating = 2;
                            else if(overallRating > 2 && overallRating <= 2.5)
                                overallRating = 2.5;
                            else if(overallRating > 2.5 && overallRating <= 3)
                                overallRating = 3;
                            else if(overallRating > 3 && overallRating <= 3.5)
                                overallRating = 3.5;
                            else if(overallRating > 3.5 && overallRating <= 4)
                                overallRating = 4;
                            else if(overallRating > 4 && overallRating <= 4.5)
                                overallRating = 4.5;
                            else if(overallRating > 4.5 && overallRating <= 5)
                                overallRating = 5;
                        }
                    }

                    @if(overallRating == 5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    }
                    else if(overallRating == 4.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    }
                    else if(overallRating == 4)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 3.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 3)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 2.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 2)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 1.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 1)
                    {
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else
                    {
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    <span>(@reviewCount Reviews)</span>
                </div>
                <div class="call">
                    <i class="fas fa-phone-volume"></i> @Model.Listing.ListingPhone
                </div>
                <div class="listing-items">
                    <a href="@Url.Action("BrandDetail", "Listing", new { slug = Model.Listing.ListingBrand.ListingBrandSlug })">
                        <i class="far fa-edit"></i> @Model.Listing.ListingBrand.ListingBrandName
                    </a>
                    <a href="@Url.Action("AddWishlist", "Customer", new { id = Model.Listing.Id })">
                        <i class="fas fa-heart"></i> Add to Wishlist
                    </a>
                    <a href="" data-toggle="modal" data-target="#send_message_modal">
                        <i class="far fa-envelope"></i> Send Message
                    </a>
                    <a href="" data-toggle="modal" data-target="#report_modal">
                        <i class="far fa-flag"></i> Report
                    </a>
                </div>

                @if(Model.Listing.ListingSocialItems.Any())
                {
                    <div class="social">
                        <ul>
                            @foreach(var row in Model.Listing.ListingSocialItems)
                            {
                                var iconCode = row.SocialIcon switch
                                {
                                    "Facebook" => "fab fa-facebook-f",
                                    "Twitter" => "fab fa-twitter",
                                    "LinkedIn" => "fab fa-linkedin-in",
                                    "YouTube" => "fab fa-youtube",
                                    "Pinterest" => "fab fa-pinterest-p",
                                    "GooglePlus" => "fab fa-google-plus-g",
                                    "Instagram" => "fab fa-instagram",
                                    _ => "fas fa-link"
                                };

                                <li>
                                    <a href="@row.SocialUrl"><i class="@iconCode"></i></a>
                                </li>
                            }
                        </ul>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-sm-12">
                <div class="listing-page">

                    <!-- Description Section -->
                    <h2><i class="fas fa-folder"></i> Description</h2>
                    <p>
                        @Html.Raw(Model.Listing.ListingDescription)
                    </p>

                    <!-- Photos Section -->
                    <h2><i class="fas fa-camera"></i> Photos</h2>
                    @if(Model.Listing.ListingPhotos.Any())
                    {
                        <div class="row">
                            @foreach(var photo in Model.Listing.ListingPhotos)
                            {
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="gallery-photo">
                                        <a href="~/uploads/listing_photos/@photo.PhotoName" class="magnific">
                                            <img src="~/uploads/listing_photos/@photo.PhotoName" alt="@Model.Listing.ListingName">
                                            <div class="gallery-photo-bg"></div>
                                            <div class="plus-icon">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-photos">
                            <p class="text-muted">No additional photos available for this listing.</p>
                        </div>
                    }

                    <!-- Videos Section -->
                    <h2><i class="fas fa-video"></i> Videos</h2>
                    @if(Model.Listing.ListingVideos.Any())
                    {
                        <div class="row">
                            @foreach(var video in Model.Listing.ListingVideos)
                            {
                                <div class="col-lg-6 col-md-12 col-sm-12">
                                    <div class="video-item">
                                        @{
                                            var embedUrl = "";

                                            if(video.VideoId.Contains("youtube.com") || video.VideoId.Contains("youtu.be"))
                                            {
                                                var videoId = "";
                                                if(video.VideoId.Contains("youtube.com/watch?v="))
                                                {
                                                    videoId = video.VideoId.Split("v=")[1].Split("&")[0];
                                                }
                                                else if(video.VideoId.Contains("youtu.be/"))
                                                {
                                                    videoId = video.VideoId.Split("youtu.be/")[1].Split("?")[0];
                                                }
                                                else if(video.VideoId.Contains("youtube.com/embed/"))
                                                {
                                                    videoId = video.VideoId.Split("embed/")[1].Split("?")[0];
                                                }
                                                else
                                                {
                                                    // Assume it's just the video ID
                                                    videoId = video.VideoId;
                                                }
                                                embedUrl = $"https://www.youtube.com/embed/{videoId}";
                                            }
                                            else if(video.VideoId.Contains("vimeo.com"))
                                            {
                                                var videoId = video.VideoId.Split("/").Last().Split("?")[0];
                                                embedUrl = $"https://player.vimeo.com/video/{videoId}";
                                            }
                                            else if(video.VideoId.StartsWith("http"))
                                            {
                                                // Full URL provided
                                                embedUrl = video.VideoId;
                                            }
                                            else
                                            {
                                                // Assume it's a YouTube video ID
                                                embedUrl = $"https://www.youtube.com/embed/{video.VideoId}";
                                            }
                                        }

                                        <iframe src="@embedUrl"
                                                width="100%"
                                                height="315"
                                                frameborder="0"
                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                                                allowfullscreen></iframe>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-videos">
                            <p class="text-muted">No videos available for this listing.</p>
                        </div>
                    }

                    <!-- Map Section -->
                    <h2><i class="fas fa-map-marker-alt"></i> Location</h2>
                    <div class="map">
                        @if(!string.IsNullOrEmpty(Model.Listing.ListingMap))
                        {
                            @Html.Raw(Model.Listing.ListingMap)
                        }
                        else
                        {
                            <p class="text-muted">Map not available for this listing.</p>
                        }
                    </div>

                    <!-- Additional Features Section -->
                    @if(Model.Listing.ListingAdditionalFeatures.Any())
                    {
                        <h2><i class="fas fa-star"></i> Additional Features</h2>
                        <div class="row">
                            @foreach(var feature in Model.Listing.ListingAdditionalFeatures)
                            {
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="feature-item">
                                        <i class="fas fa-check"></i> @feature.AdditionalFeatureName
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <!-- Amenities Section -->
                    @if(Model.Listing.ListingAmenities.Any())
                    {
                        <h2><i class="fas fa-list"></i> Amenities</h2>
                        <div class="row">
                            @foreach(var amenity in Model.Listing.ListingAmenities)
                            {
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="amenity-item">
                                        <i class="fas fa-check"></i> @amenity.Amenity.AmenityName
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <!-- Listing Details Section -->
                    <h2><i class="fas fa-info-circle"></i> Listing Details</h2>
                    <div class="listing-details">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>@Model.Listing.ListingType</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Brand:</strong></td>
                                        <td>
                                            <a href="@Url.Action("BrandDetail", "Listing", new { slug = Model.Listing.ListingBrand.ListingBrandSlug })">
                                                @Model.Listing.ListingBrand.ListingBrandName
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Location:</strong></td>
                                        <td>
                                            <a href="@Url.Action("LocationDetail", "Listing", new { slug = Model.Listing.ListingLocation.ListingLocationSlug })">
                                                @Model.Listing.ListingLocation.ListingLocationName
                                            </a>
                                        </td>
                                    </tr>
                                    @if(Model.Listing.ListingModelYear.HasValue)
                                    {
                                        <tr>
                                            <td><strong>Model Year:</strong></td>
                                            <td>@Model.Listing.ListingModelYear</td>
                                        </tr>
                                    }
                                    @if(Model.Listing.ListingYear.HasValue)
                                    {
                                        <tr>
                                            <td><strong>Year:</strong></td>
                                            <td>@Model.Listing.ListingYear</td>
                                        </tr>
                                    }
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    @if(!string.IsNullOrEmpty(Model.Listing.ListingCondition))
                                    {
                                        <tr>
                                            <td><strong>Condition:</strong></td>
                                            <td>@Model.Listing.ListingCondition</td>
                                        </tr>
                                    }
                                    @if(Model.Listing.ListingMileage.HasValue)
                                    {
                                        <tr>
                                            <td><strong>Mileage:</strong></td>
                                            <td>@Model.Listing.ListingMileage miles</td>
                                        </tr>
                                    }
                                    @if(!string.IsNullOrEmpty(Model.Listing.ListingFuelType))
                                    {
                                        <tr>
                                            <td><strong>Fuel Type:</strong></td>
                                            <td>@Model.Listing.ListingFuelType</td>
                                        </tr>
                                    }
                                    @if(!string.IsNullOrEmpty(Model.Listing.ListingTransmission))
                                    {
                                        <tr>
                                            <td><strong>Transmission:</strong></td>
                                            <td>@Model.Listing.ListingTransmission</td>
                                        </tr>
                                    }
                                    @if(!string.IsNullOrEmpty(Model.Listing.ListingEngineCapacity))
                                    {
                                        <tr>
                                            <td><strong>Engine Capacity:</strong></td>
                                            <td>@Model.Listing.ListingEngineCapacity</td>
                                        </tr>
                                    }
                                    @if(!string.IsNullOrEmpty(Model.Listing.ListingEngineSize))
                                    {
                                        <tr>
                                            <td><strong>Engine Size:</strong></td>
                                            <td>@Model.Listing.ListingEngineSize</td>
                                        </tr>
                                    }
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12">
                <div class="listing-sidebar">

                    <!-- Agent Information -->
                    @if(Model.Listing.User != null || Model.Listing.Admin != null)
                    {
                        <div class="widget">
                            <h3>Agent Information</h3>
                            <div class="agent-info">
                                @if(Model.Listing.User != null)
                                {
                                    <div class="agent-photo">
                                        @if(!string.IsNullOrEmpty(Model.Listing.User.Photo))
                                        {
                                            <img src="~/uploads/user_photos/@Model.Listing.User.Photo" alt="@Model.Listing.User.Name">
                                        }
                                        else
                                        {
                                            <img src="~/uploads/user_photos/default_photo.jpg" alt="@Model.Listing.User.Name">
                                        }
                                    </div>
                                    <div class="agent-details">
                                        <h4>@Model.Listing.User.Name</h4>
                                        <p>@Model.Listing.User.Email</p>
                                        @if(!string.IsNullOrEmpty(Model.Listing.User.Phone))
                                        {
                                            <p><i class="fas fa-phone"></i> @Model.Listing.User.Phone</p>
                                        }
                                        @if(!string.IsNullOrEmpty(Model.Listing.User.Address))
                                        {
                                            <p><i class="fas fa-map-marker-alt"></i> @Model.Listing.User.Address</p>
                                        }
                                    </div>
                                }
                                else if(Model.Listing.Admin != null)
                                {
                                    <div class="agent-photo">
                                        @if(!string.IsNullOrEmpty(Model.Listing.Admin.Photo))
                                        {
                                            <img src="~/uploads/admin_photos/@Model.Listing.Admin.Photo" alt="@Model.Listing.Admin.Name">
                                        }
                                        else
                                        {
                                            <img src="~/uploads/admin_photos/default_photo.jpg" alt="@Model.Listing.Admin.Name">
                                        }
                                    </div>
                                    <div class="agent-details">
                                        <h4>@Model.Listing.Admin.Name</h4>
                                        <p>@Model.Listing.Admin.Email</p>
                                        @if(!string.IsNullOrEmpty(Model.Listing.Admin.Phone))
                                        {
                                            <p><i class="fas fa-phone"></i> @Model.Listing.Admin.Phone</p>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Contact Form Widget -->
                    <div class="widget">
                        <h3>Contact Agent</h3>
                        <div class="contact-form">
                            <form action="#" method="post">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Your Name" required>
                                </div>
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Your Email" required>
                                </div>
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Your Phone">
                                </div>
                                <div class="form-group">
                                    <textarea class="form-control" rows="4" placeholder="Your Message" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">Send Message</button>
                            </form>
                        </div>
                    </div>

                    <!-- Share Widget -->
                    <div class="widget">
                        <h3>Share This Listing</h3>
                        <div class="sharethis-inline-share-buttons"></div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="row mt-5">
            <div class="col-12">
                <h2><i class="fas fa-star"></i> Reviews (@Model.TotalReviews)</h2>

                @if(Model.TotalReviews > 0)
                {
                    <div class="overall-rating mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="rating-summary">
                                    <div class="average-rating">
                                        <span class="rating-number">@Model.AverageRating.ToString("F1")</span>
                                        <div class="rating-stars">
                                            @{
                                                var avgRating = (double)Model.AverageRating;
                                                for(int i = 1; i <= 5; i++)
                                                {
                                                    if(i <= avgRating)
                                                    {
                                                        <i class="fas fa-star"></i>
                                                    }
                                                    else if(i - 0.5 <= avgRating)
                                                    {
                                                        <i class="fas fa-star-half-alt"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="far fa-star"></i>
                                                    }
                                                }
                                            }
                                        </div>
                                        <span class="total-reviews">Based on @Model.TotalReviews reviews</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="reviews-list">
                        @foreach(var review in Model.Reviews.Take(5))
                        {
                            <div class="review-item">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="reviewer-photo">
                                            @if(review.AgentType == "User" && review.User != null)
                                            {
                                                @if(!string.IsNullOrEmpty(review.User.Photo))
                                                {
                                                    <img src="~/uploads/user_photos/@review.User.Photo" alt="@review.User.Name">
                                                }
                                                else
                                                {
                                                    <img src="~/uploads/user_photos/default_photo.jpg" alt="@review.User.Name">
                                                }
                                            }
                                            else if(review.AgentType == "Admin" && review.Admin != null)
                                            {
                                                @if(!string.IsNullOrEmpty(review.Admin.Photo))
                                                {
                                                    <img src="~/uploads/admin_photos/@review.Admin.Photo" alt="@review.Admin.Name">
                                                }
                                                else
                                                {
                                                    <img src="~/uploads/admin_photos/default_photo.jpg" alt="@review.Admin.Name">
                                                }
                                            }
                                            else
                                            {
                                                <img src="~/uploads/user_photos/default_photo.jpg" alt="Reviewer">
                                            }
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="review-content">
                                            <div class="reviewer-name">
                                                @if(review.AgentType == "User" && review.User != null)
                                                {
                                                    @review.User.Name
                                                }
                                                else if(review.AgentType == "Admin" && review.Admin != null)
                                                {
                                                    @review.Admin.Name
                                                }
                                                else
                                                {
                                                    <span>Anonymous</span>
                                                }
                                            </div>
                                            <div class="review-rating">
                                                @for(int i = 1; i <= 5; i++)
                                                {
                                                    if(i <= review.Rating)
                                                    {
                                                        <i class="fas fa-star"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="far fa-star"></i>
                                                    }
                                                }
                                            </div>
                                            <div class="review-date">
                                                @review.CreatedAt.ToString("MMMM dd, yyyy")
                                            </div>
                                            @if(!string.IsNullOrEmpty(review.ReviewText))
                                            {
                                                <div class="review-text">
                                                    @review.ReviewText
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    @if(Model.Reviews.Count > 5)
                    {
                        <div class="text-center mt-3">
                            <button class="btn btn-outline-primary" onclick="loadMoreReviews()">Load More Reviews</button>
                        </div>
                    }
                }
                else
                {
                    <div class="no-reviews">
                        <p class="text-muted">No reviews yet. Be the first to review this listing!</p>
                    </div>
                }

                <!-- Add Review Form (for logged in users) -->
                <div class="add-review-section mt-4">
                    <h3>Add Your Review</h3>
                    <form action="@Url.Action("SubmitReview", "Customer", new { Area = "Customer" })" method="post">
                        <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                        <div class="form-group">
                            <label>Rating</label>
                            <div class="rating-input">
                                <input type="radio" name="Rating" value="5" id="star5">
                                <label for="star5"><i class="fas fa-star"></i></label>
                                <input type="radio" name="Rating" value="4" id="star4">
                                <label for="star4"><i class="fas fa-star"></i></label>
                                <input type="radio" name="Rating" value="3" id="star3">
                                <label for="star3"><i class="fas fa-star"></i></label>
                                <input type="radio" name="Rating" value="2" id="star2">
                                <label for="star2"><i class="fas fa-star"></i></label>
                                <input type="radio" name="Rating" value="1" id="star1">
                                <label for="star1"><i class="fas fa-star"></i></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="reviewText">Your Review</label>
                            <textarea class="form-control" name="ReviewText" id="reviewText" rows="4" placeholder="Share your experience with this listing..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Related Listings Section -->
        @if(Model.RelatedListings.Any())
        {
            <div class="row mt-5">
                <div class="col-12">
                    <h2><i class="fas fa-car"></i> Related Listings</h2>
                    <div class="row">
                        @foreach(var listing in Model.RelatedListings.Take(6))
                        {
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="listing-item">
                                    <div class="photo">
                                        <a href="@Url.Action("Detail", "Listing", new { id = listing.Id, slug = listing.ListingSlug })">
                                            <img src="~/uploads/listing_featured_photos/@listing.ListingFeaturedPhoto" alt="@listing.ListingName">
                                        </a>
                                        <div class="brand">
                                            <a href="@Url.Action("BrandDetail", "Listing", new { slug = listing.ListingBrand?.ListingBrandSlug })">
                                                @listing.ListingBrand?.ListingBrandName
                                            </a>
                                        </div>
                                        <div class="wishlist">
                                            <a href="@Url.Action("AddToWishlist", "Customer", new { id = listing.Id })">
                                                <i class="fas fa-heart"></i>
                                            </a>
                                        </div>
                                        @if(listing.IsFeatured)
                                        {
                                            <div class="featured-text">Featured</div>
                                        }
                                    </div>
                                    <div class="text">
                                        <div class="type-price">
                                            <div class="type">
                                                @if(listing.ListingType == "New Car")
                                                {
                                                    <div class="inner-new">New Car</div>
                                                }
                                                else
                                                {
                                                    <div class="inner-used">Used Car</div>
                                                }
                                            </div>
                                            <div class="price">
                                                @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                                                {
                                                    @($"${listing.ListingPrice:N0}")
                                                }
                                                else
                                                {
                                                    @($"{Model.CurrentCurrencySymbol}{(listing.ListingPrice * Model.CurrentCurrencyValue):N0}")
                                                }
                                            </div>
                                        </div>
                                        <h3>
                                            <a href="@Url.Action("Detail", "Listing", new { id = listing.Id, slug = listing.ListingSlug })">
                                                @listing.ListingName
                                            </a>
                                        </h3>
                                        <div class="location">
                                            <a href="@Url.Action("LocationDetail", "Listing", new { slug = listing.ListingLocation?.ListingLocationSlug })">
                                                <i class="fas fa-map-marker-alt"></i> @listing.ListingLocation?.ListingLocationName
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Send Message Modal -->
<div class="modal fade" id="send_message_modal" tabindex="-1" role="dialog" aria-labelledby="sendMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendMessageModalLabel">Send Message to Agent</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="@Url.Action("SendMessage", "Customer", new { Area = "Customer" })" method="post">
                <div class="modal-body">
                    <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                    <div class="form-group">
                        <label for="senderName">Your Name</label>
                        <input type="text" class="form-control" id="senderName" name="SenderName" required>
                    </div>
                    <div class="form-group">
                        <label for="senderEmail">Your Email</label>
                        <input type="email" class="form-control" id="senderEmail" name="SenderEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="senderPhone">Your Phone</label>
                        <input type="text" class="form-control" id="senderPhone" name="SenderPhone">
                    </div>
                    <div class="form-group">
                        <label for="messageText">Message</label>
                        <textarea class="form-control" id="messageText" name="MessageText" rows="4" required placeholder="I'm interested in this listing..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div class="modal fade" id="report_modal" tabindex="-1" role="dialog" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">Report This Listing</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="@Url.Action("ReportListing", "Customer", new { Area = "Customer" })" method="post">
                <div class="modal-body">
                    <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                    <div class="form-group">
                        <label for="reporterName">Your Name</label>
                        <input type="text" class="form-control" id="reporterName" name="ReporterName" required>
                    </div>
                    <div class="form-group">
                        <label for="reporterEmail">Your Email</label>
                        <input type="email" class="form-control" id="reporterEmail" name="ReporterEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="reportReason">Reason for Report</label>
                        <select class="form-control" id="reportReason" name="ReportReason" required>
                            <option value="">Select a reason</option>
                            <option value="Inappropriate Content">Inappropriate Content</option>
                            <option value="Spam">Spam</option>
                            <option value="Fraudulent Listing">Fraudulent Listing</option>
                            <option value="Incorrect Information">Incorrect Information</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="reportDescription">Description</label>
                        <textarea class="form-control" id="reportDescription" name="ReportDescription" rows="4" required placeholder="Please provide details about your report..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Submit Report</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Photo gallery magnific popup
    $(document).ready(function() {
        $('.magnific').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            }
        });

        // Rating input functionality
        $('.rating-input input[type="radio"]').change(function() {
            var rating = $(this).val();
            $('.rating-input label').removeClass('active');
            $(this).nextAll('label').addBack().addClass('active');
        });

        // Initialize ShareThis buttons
        if (typeof __sharethis__ !== 'undefined') {
            __sharethis__.initialize();
        }
    });

    function loadMoreReviews() {
        // Implementation for loading more reviews via AJAX
        // This would typically make an AJAX call to load additional reviews
        console.log('Load more reviews functionality to be implemented');
    }
</script>

<style>
    /* Additional styles for the listing detail page */
    .listing-details table td {
        padding: 8px 0;
        border: none;
    }

    .feature-item, .amenity-item {
        padding: 5px 0;
        border-bottom: 1px solid #eee;
    }

    .feature-item:last-child, .amenity-item:last-child {
        border-bottom: none;
    }

    .agent-info {
        text-align: center;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .agent-photo img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 15px;
    }

    .review-item {
        padding: 20px 0;
        border-bottom: 1px solid #eee;
    }

    .review-item:last-child {
        border-bottom: none;
    }

    .reviewer-photo img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
    }

    .review-rating {
        color: #ffc107;
        margin: 5px 0;
    }

    .review-date {
        color: #6c757d;
        font-size: 0.9em;
        margin-bottom: 10px;
    }

    .rating-input {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .rating-input input[type="radio"] {
        display: none;
    }

    .rating-input label {
        cursor: pointer;
        color: #ddd;
        font-size: 20px;
        margin-right: 5px;
        transition: color 0.2s;
    }

    .rating-input label:hover,
    .rating-input label.active {
        color: #ffc107;
    }

    .rating-input input[type="radio"]:checked ~ label {
        color: #ffc107;
    }

    .overall-rating {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .average-rating {
        text-align: center;
    }

    .rating-number {
        font-size: 3em;
        font-weight: bold;
        color: #ffc107;
    }

    .rating-stars {
        color: #ffc107;
        font-size: 1.5em;
        margin: 10px 0;
    }

    .total-reviews {
        color: #6c757d;
    }

    .widget {
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 5px;
    }

    .widget h3 {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e00445;
    }

    .contact-form .form-group {
        margin-bottom: 15px;
    }

    .contact-form .btn {
        background: #e00445;
        border-color: #e00445;
    }

    .contact-form .btn:hover {
        background: #c00337;
        border-color: #c00337;
    }

    /* Video Gallery Styles */
    .video-item {
        margin-bottom: 30px;
        position: relative;
        background: #000;
        border-radius: 5px;
        overflow: hidden;
    }

    .video-item iframe {
        width: 100%;
        height: 315px;
        border: none;
        display: block;
    }

    /* Related Listings Styles */
    .listing-item {
        background: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 30px;
    }

    .listing-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .listing-item .photo {
        position: relative;
        overflow: hidden;
    }

    .listing-item .photo img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .listing-item:hover .photo img {
        transform: scale(1.05);
    }

    .listing-item .brand {
        position: absolute;
        top: 15px;
        left: 15px;
        background: rgba(224, 4, 69, 0.9);
        color: #fff;
        padding: 5px 10px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 600;
    }

    .listing-item .brand a {
        color: #fff;
        text-decoration: none;
    }

    .listing-item .wishlist {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(255, 255, 255, 0.9);
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .listing-item .wishlist:hover {
        background: #e00445;
    }

    .listing-item .wishlist a {
        color: #e00445;
        font-size: 16px;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .listing-item .wishlist:hover a {
        color: #fff;
    }

    .listing-item .featured-text {
        position: absolute;
        bottom: 15px;
        left: 15px;
        background: #28a745;
        color: #fff;
        padding: 5px 10px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 600;
    }

    .listing-item .text {
        padding: 20px;
    }

    .listing-item .type-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .listing-item .type .inner-new {
        background: #28a745;
        color: #fff;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .listing-item .type .inner-used {
        background: #ffc107;
        color: #000;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .listing-item .price {
        font-size: 18px;
        font-weight: 700;
        color: #e00445;
    }

    .listing-item h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    .listing-item h3 a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .listing-item h3 a:hover {
        color: #e00445;
    }

    .listing-item .location {
        color: #666;
        font-size: 14px;
    }

    .listing-item .location a {
        color: #666;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .listing-item .location a:hover {
        color: #e00445;
    }

    .listing-item .location i {
        margin-right: 5px;
        color: #e00445;
    }

    /* Section Headings */
    .listing-page h2 {
        color: #333;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e00445;
        display: flex;
        align-items: center;
    }

    .listing-page h2 i {
        margin-right: 10px;
        color: #e00445;
    }

    /* Photo Gallery Improvements */
    .gallery-photo {
        position: relative;
        margin-bottom: 20px;
        border-radius: 5px;
        overflow: hidden;
    }

    .gallery-photo img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .gallery-photo:hover img {
        transform: scale(1.05);
    }
</style>
