using CarPointCMS.Data;
using CarPointCMS.Models.Entities;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Areas.Customer.Controllers
{
    [Area("Customer")]
    public class ListingController : Controller
    {
        private readonly ApplicationDbContext _db;
        private readonly ILogger<ListingController> _logger;

        public ListingController(ApplicationDbContext db, ILogger<ListingController> logger)
        {
            _db = db;
            _logger = logger;
        }

        // GET: /Listing
        public async Task<IActionResult> Index(string? searchText, int? brandId, int? locationId,
            string? listingType, decimal? minPrice, decimal? maxPrice, int? pageIndex)
        {
            try
            {
                var query = _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.User)
                    .Where(l => l.ListingStatus == ListingStatus.Active)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(searchText))
                {
                    query = query.Where(l => l.ListingName.Contains(searchText) ||
                                           l.ListingDescription.Contains(searchText));
                }

                if (brandId.HasValue)
                {
                    query = query.Where(l => l.ListingBrandId == brandId.Value);
                }

                if (locationId.HasValue)
                {
                    query = query.Where(l => l.ListingLocationId == locationId.Value);
                }

                if (!string.IsNullOrEmpty(listingType))
                {
                    query = query.Where(l => l.ListingType == listingType);
                }

                if (minPrice.HasValue)
                {
                    query = query.Where(l => l.ListingPrice >= minPrice.Value);
                }

                if (maxPrice.HasValue)
                {
                    query = query.Where(l => l.ListingPrice <= maxPrice.Value);
                }

                // Pagination
                int pageSize = 12;
                var listings = await PaginatedList<Listing>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new ListingResultViewModel
                {
                    PageListingItem = await _db.PageListingItems.FirstOrDefaultAsync() ?? new PageListingItem(),
                    Listings = listings.ToList(),
                    ListingBrands = await _db.ListingBrands.ToListAsync(),
                    ListingLocations = await _db.ListingLocations.ToListAsync(),
                    Amenities = await _db.Amenities.ToListAsync(),
                    SearchText = searchText,
                    ListingType = listingType,
                    SelectedBrands = brandId.HasValue ? new List<int> { brandId.Value } : new List<int>(),
                    SelectedLocations = locationId.HasValue ? new List<int> { locationId.Value } : new List<int>(),
                    MinPrice = minPrice ?? 0,
                    MaxPrice = maxPrice ?? 0,
                    CurrentPage = pageIndex ?? 1,
                    TotalPages = listings.TotalPages,
                    TotalCount = listings.TotalCount
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading listings");
                return View(new ListingResultViewModel());
            }
        }

        // GET: /Listing/Detail/{id}
        public async Task<IActionResult> Detail(int id)
        {
            try
            {
                var listing = await _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.User)
                    .Include(l => l.Admin)
                    .Include(l => l.ListingPhotos)
                    .Include(l => l.ListingVideos)
                    .Include(l => l.ListingAmenities)
                        .ThenInclude(la => la.Amenity)
                    .Include(l => l.ListingSocialItems)
                    .Include(l => l.ListingAdditionalFeatures)
                    .FirstOrDefaultAsync(l => l.Id == id && l.ListingStatus == ListingStatus.Active);

                if (listing == null)
                {
                    return NotFound();
                }

                // Get reviews for this listing with user/admin details
                var reviews = await _db.Reviews
                    .Include(r => r.User)
                    .Include(r => r.Admin)
                    .Where(r => r.ListingId == id)
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();

                // Get related listings (same brand or location)
                var relatedListings = await _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Where(l => l.Id != id &&
                               l.ListingStatus == ListingStatus.Active &&
                               (l.ListingBrandId == listing.ListingBrandId ||
                                l.ListingLocationId == listing.ListingLocationId))
                    .Take(6)
                    .ToListAsync();

                // Get general settings for currency and other settings
                var generalSettings = await _db.GeneralSettings.FirstOrDefaultAsync() ?? new GeneralSetting();

                // Get current currency from session or default
                var currentCurrencyName = HttpContext.Session.GetString("session_currency_name") ?? "USD";
                var currentCurrencySymbol = HttpContext.Session.GetString("session_currency_symbol") ?? "$";
                var currentCurrencyValue = decimal.Parse(HttpContext.Session.GetString("session_currency_value") ?? "1");

                var model = new ListingDetailViewModel
                {
                    Listing = listing,
                    Reviews = reviews,
                    RelatedListings = relatedListings,
                    AverageRating = reviews.Any() ? (decimal)reviews.Average(r => r.Rating) : 0,
                    TotalReviews = reviews.Count,
                    GeneralSettings = generalSettings,
                    CurrentCurrencyName = currentCurrencyName,
                    CurrentCurrencySymbol = currentCurrencySymbol,
                    CurrentCurrencyValue = currentCurrencyValue
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading listing detail for ID: {ListingId}", id);
                return NotFound();
            }
        }

        // GET: /Listing/Brands
        public async Task<IActionResult> Brands()
        {
            try
            {
                var brands = await _db.ListingBrands.ToListAsync();
                var listings = await _db.Listings
                    .Where(l => l.ListingStatus == ListingStatus.Active)
                    .ToListAsync();

                var model = new ListingBrandsViewModel
                {
                    PageListingBrandItem = await _db.PageListingBrandItems.FirstOrDefaultAsync(),
                    ListingBrands = brands,
                    Listings = listings
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading listing brands");
                return View(new ListingBrandsViewModel());
            }
        }

        // GET: /Listing/Locations
        public async Task<IActionResult> Locations()
        {
            try
            {
                var locations = await _db.ListingLocations.ToListAsync();
                var listings = await _db.Listings
                    .Where(l => l.ListingStatus == "Active")
                    .ToListAsync();

                var model = new ListingLocationsViewModel
                {
                    PageListingLocationItem = await _db.PageListingLocationItems.FirstOrDefaultAsync(),
                    ListingLocations = locations,
                    Listings = listings
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading listing locations");
                return View(new ListingLocationsViewModel());
            }
        }

        // GET: /Listing/BrandDetail/{id}
        public async Task<IActionResult> BrandDetail(int id, int? pageIndex)
        {
            try
            {
                var brand = await _db.ListingBrands.FirstOrDefaultAsync(b => b.Id == id);
                if (brand == null)
                {
                    return NotFound();
                }

                var query = _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.User)
                    .Where(l => l.ListingBrandId == id && l.ListingStatus == "Active");

                int pageSize = 12;
                var listings = await PaginatedList<Listing>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new ListingBrandDetailViewModel
                {
                    Brand = brand,
                    Listings = listings.ToList(),
                    CurrentPage = pageIndex ?? 1,
                    TotalPages = listings.TotalPages,
                    TotalCount = listings.TotalCount
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading brand detail for ID: {BrandId}", id);
                return NotFound();
            }
        }

        // GET: /Listing/LocationDetail/{id}
        public async Task<IActionResult> LocationDetail(int id, int? pageIndex)
        {
            try
            {
                var location = await _db.ListingLocations.FirstOrDefaultAsync(l => l.Id == id);
                if (location == null)
                {
                    return NotFound();
                }

                var query = _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.User)
                    .Where(l => l.ListingLocationId == id && l.ListingStatus == "Active");

                int pageSize = 12;
                var listings = await PaginatedList<Listing>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new ListingLocationDetailViewModel
                {
                    Location = location,
                    Listings = listings.ToList(),
                    CurrentPage = pageIndex ?? 1,
                    TotalPages = listings.TotalPages,
                    TotalCount = listings.TotalCount
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading location detail for ID: {LocationId}", id);
                return NotFound();
            }
        }

        // AJAX endpoint for search
        [HttpPost]
        public async Task<IActionResult> AjaxSearch([FromBody] AjaxSearchRequest request)
        {
            try
            {
                var query = _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.User)
                    .Where(l => l.ListingStatus == "Active")
                    .AsQueryable();

                // Apply filters from request
                if (!string.IsNullOrEmpty(request.SearchText))
                {
                    query = query.Where(l => l.ListingName.Contains(request.SearchText));
                }

                if (request.BrandIds?.Any() == true)
                {
                    query = query.Where(l => request.BrandIds.Contains(l.ListingBrandId));
                }

                if (request.LocationIds?.Any() == true)
                {
                    query = query.Where(l => request.LocationIds.Contains(l.ListingLocationId));
                }

                var listings = await query.Take(20).ToListAsync();

                var model = new AjaxSearchListingsViewModel
                {
                    Listings = listings
                };

                return PartialView("_AjaxSearchListings", model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AJAX search");
                return BadRequest("Search failed");
            }
        }
    }

    public class AjaxSearchRequest
    {
        public string? SearchText { get; set; }
        public List<int>? BrandIds { get; set; }
        public List<int>? LocationIds { get; set; }
        public List<int>? AmenityIds { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public string? ListingType { get; set; }
    }
}
